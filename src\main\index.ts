import { app, BrowserWindow } from 'electron'
import path from 'node:path'

import { registerRoute } from 'lib/electron-router-dom'
import { setupIpcHandlers } from './ipcHandlers'

async function createMainWindow() {
  const mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    fullscreenable: true,
    show: false,
    resizable: true,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: true, // Enable Node.js integration
      contextIsolation: false, // WARNING: Disabling contextIsolation is not recommended for production builds due to security risks.
      // Consider using a preload script to expose necessary APIs safely.
      webSecurity: true // Keep webSecurity enabled by default when nodeIntegration is true
    }
  })

  registerRoute({
    id: 'main',
    browserWindow: mainWindow,
    htmlFile: path.join(__dirname, '../renderer/index.html'),
  })

  mainWindow.on('ready-to-show', mainWindow.show)
}

app.whenReady().then(() => {
  setupIpcHandlers()
  createMainWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createMainWindow()
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})