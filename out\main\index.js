"use strict";
const electron = require("electron");
const path = require("node:path");
const os = require("node:os");
const electronRouterDom = require("electron-router-dom");
const axios = require("axios");
const { Router, registerRoute, settings } = electronRouterDom.createElectronRouter({
  port: 4927,
  types: {
    ids: ["main"]
  }
});
function setupIpcHandlers() {
  console.log("Setting up IPC handlers...");
  electron.ipcMain.handle("fetch-data", async () => {
    try {
      console.log("fetch-data handler called");
      const response = await axios.get("https://jsonplaceholder.typicode.com/todos");
      console.log("API response received, data length:", response.data.length);
      return response.data;
    } catch (error) {
      console.error("Error fetching todos:", error);
      throw error;
    }
  });
  console.log("IPC handlers setup complete");
}
async function createMainWindow() {
  const mainWindow = new electron.BrowserWindow({
    width: 900,
    height: 670,
    fullscreenable: true,
    show: false,
    resizable: true,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: true,
      // Enable Node.js integration
      contextIsolation: false,
      // WARNING: Disabling contextIsolation is not recommended for production builds due to security risks.
      // Consider using a preload script to expose necessary APIs safely.
      webSecurity: true
      // Keep webSecurity enabled by default when nodeIntegration is true
    }
  });
  registerRoute({
    id: "main",
    browserWindow: mainWindow,
    htmlFile: path.join(__dirname, "../renderer/index.html")
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
    if (process.env.NODE_ENV === "development") {
      mainWindow.webContents.openDevTools();
    }
  });
}
electron.app.setPath("userData", path.join(os.tmpdir(), "electron-app-data"));
electron.app.commandLine.appendSwitch("--disable-gpu-sandbox");
electron.app.commandLine.appendSwitch("--disable-software-rasterizer");
electron.app.commandLine.appendSwitch("--disable-gpu");
electron.app.whenReady().then(() => {
  setupIpcHandlers();
  createMainWindow();
  electron.app.on("activate", () => {
    if (electron.BrowserWindow.getAllWindows().length === 0) createMainWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
