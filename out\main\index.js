"use strict";
const electron = require("electron");
const path = require("node:path");
const electronRouterDom = require("electron-router-dom");
const axios = require("axios");
const { Router, registerRoute, settings } = electronRouterDom.createElectronRouter({
  port: 4927,
  types: {
    ids: ["main"]
  }
});
function setupIpcHandlers() {
  electron.ipcMain.handle("fetch-data", async () => {
    try {
      const response = await axios.get("https://jsonplaceholder.typicode.com/todos");
      return response.data;
    } catch (error) {
      console.error("Error fetching todos:", error);
      throw error;
    }
  });
}
async function createMainWindow() {
  const mainWindow = new electron.BrowserWindow({
    width: 900,
    height: 670,
    fullscreenable: true,
    show: false,
    resizable: true,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: true,
      // Enable Node.js integration
      contextIsolation: false,
      // WARNING: Disabling contextIsolation is not recommended for production builds due to security risks.
      // Consider using a preload script to expose necessary APIs safely.
      webSecurity: true
      // Keep webSecurity enabled by default when nodeIntegration is true
    }
  });
  registerRoute({
    id: "main",
    browserWindow: mainWindow,
    htmlFile: path.join(__dirname, "../renderer/index.html")
  });
  mainWindow.on("ready-to-show", mainWindow.show);
}
electron.app.whenReady().then(() => {
  setupIpcHandlers();
  createMainWindow();
  electron.app.on("activate", () => {
    if (electron.BrowserWindow.getAllWindows().length === 0) createMainWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
