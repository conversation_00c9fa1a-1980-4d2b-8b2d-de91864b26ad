import { useState, useEffect } from 'react';
import { ipc<PERSON><PERSON><PERSON> } from 'electron';

interface Todo {
  userId: number;
  id: number;
  title: string;
  completed: boolean;
}

export function TodosScreen() {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTodos = async () => {
      try {
        setLoading(true);
        const data = await ipcRenderer.invoke('fetch-data');
        console.log('Data received from main process:', data);
        setTodos(data);
        setError(null);
      } catch (error) {
        console.error('Error receiving data:', error);
        setError('Failed to fetch todos.');
        setTodos([]); // Clear todos on error
      } finally {
        setLoading(false);
      }
    };

    fetchTodos();
  }, []); // Empty dependency array means this effect runs once on mount

  if (loading) {
    return <div>Loading todos...</div>;
  }

  if (error) {
    return <div>Error fetching todos: {error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Todo List</h1>
      <ul>
        {todos.map(todo => (
          <li key={todo.id} className={`border-b py-2 ${todo.completed ? 'line-through text-gray-500' : ''}`}>
            {todo.title}
          </li>
        ))}
      </ul>
    </div>
  );
}
