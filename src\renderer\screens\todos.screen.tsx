import { useState, useEffect } from 'react';
import { safeIpcInvoke } from '../utils/electron';

interface Todo {
  userId: number;
  id: number;
  title: string;
  completed: boolean;
}

export function TodosScreen() {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTodos = async () => {
      try {
        setLoading(true);
        console.log('Attempting to fetch data...');

        const data = await safeIpcInvoke('fetch-data');
        console.log('Data received from main process:', data);
        console.log('Data type:', typeof data);
        console.log('Data length:', Array.isArray(data) ? data.length : 'Not an array');

        setTodos(data.slice(0, 10)); // Use slice instead of splice to avoid mutating the original array
        setError(null);
      } catch (error) {
        console.error('Error receiving data:', error);
        console.error('Error details:', error);
        setError(`Failed to fetch todos: ${error instanceof Error ? error.message : String(error)}`);
        setTodos([]); // Clear todos on error
      } finally {
        setLoading(false);
      }
    };

    fetchTodos();
  }, []); // Empty dependency array means this effect runs once on mount

  if (loading) {
    return <div>Loading todos...</div>;
  }

  if (error) {
    return <div>Error fetching todos: {error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Todo List</h1>
      <p className="mb-4 text-sm text-gray-600">
        Showing {todos.length} todos (fetched from JSONPlaceholder API)
      </p>
      <ul>
        {todos.map(todo => (
          <li key={todo.id} className={`border-b py-2 ${todo.completed ? 'line-through text-gray-500' : ''}`}>
            <span className="font-medium">#{todo.id}</span> - {todo.title}
          </li>
        ))}
      </ul>
    </div>
  );
}
