import { useState, useEffect } from 'react';

// Try different ways to access ipc<PERSON><PERSON><PERSON>
let ipc<PERSON>enderer: any;
try {
  // Method 1: Direct import (should work with nodeIntegration: true)
  ipcRenderer = require('electron').ipcRenderer;
} catch (error) {
  console.error('Failed to import ipc<PERSON><PERSON><PERSON> via require:', error);
  try {
    // Method 2: ES6 import
    const electron = require('electron');
    ipcRenderer = electron.ipcRenderer;
  } catch (error2) {
    console.error('Failed to access ipcRenderer via electron object:', error2);
    // Method 3: Check if it's available on window (in case of preload script)
    ipcRenderer = (window as any).electron?.ipcRenderer;
  }
}

interface Todo {
  userId: number;
  id: number;
  title: string;
  completed: boolean;
}

export function TodosScreen() {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Debug: Check ipcRenderer availability
  console.log('ipcRenderer available:', !!ipcRenderer);
  console.log('ipcRenderer type:', typeof ipcRenderer);
  if (ipcRenderer) {
    console.log('ipcRenderer.invoke available:', !!ipcRenderer.invoke);
  }

  useEffect(() => {
    const fetchTodos = async () => {
      try {
        setLoading(true);
        console.log('Attempting to fetch data...');
        console.log('ipcRenderer available:', !!ipcRenderer);
        console.log('ipcRenderer.invoke available:', !!ipcRenderer?.invoke);

        if (!ipcRenderer) {
          throw new Error('ipcRenderer is not available');
        }

        if (!ipcRenderer.invoke) {
          throw new Error('ipcRenderer.invoke is not available');
        }

        const data = await ipcRenderer.invoke('fetch-data');
        console.log('Data received from main process:', data);
        console.log('Data type:', typeof data);
        console.log('Data length:', Array.isArray(data) ? data.length : 'Not an array');

        setTodos(data.splice(0, 10));
        setError(null);
      } catch (error) {
        console.error('Error receiving data:', error);
        console.error('Error details:', error);
        setError(`Failed to fetch todos: ${error instanceof Error ? error.message : String(error)}`);
        setTodos([]); // Clear todos on error
      } finally {
        setLoading(false);
      }
    };

    fetchTodos();
  }, []); // Empty dependency array means this effect runs once on mount

  if (loading) {
    return <div>Loading todos...</div>;
  }

  if (error) {
    return <div>Error fetching todos: {error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Todo List</h1>
      <ul>
        {todos.map(todo => (
          <li key={todo.id} className={`border-b py-2 ${todo.completed ? 'line-through text-gray-500' : ''}`}>
            {todo.title}
          </li>
        ))}
      </ul>
    </div>
  );
}
