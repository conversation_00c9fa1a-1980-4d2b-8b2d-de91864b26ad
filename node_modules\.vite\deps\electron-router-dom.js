import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  createHash<PERSON>outer,
  createRoutesFromChildren
} from "./chunk-JRLHUAM5.js";
import "./chunk-K23GC2QC.js";
import {
  require_electron
} from "./chunk-TCCOKEQM.js";
import {
  require_jsx_runtime
} from "./chunk-6PXSGDAH.js";
import {
  require_react
} from "./chunk-DRWLMN53.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/electron-router-dom/dist/esm/index.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());
function toLowerCaseKeys(target) {
  const transformedObject = Object.keys(target).reduce((acc, key) => ({
    ...acc,
    [key.toLowerCase()]: target[key]
  }), {});
  return transformedObject;
}
function Router({ _providerProps, ...routes }) {
  var _a, _b;
  const selectAllSlashes = /\//g;
  const rawId = ((_b = (_a = location.hash.split(selectAllSlashes)) == null ? void 0 : _a[1]) == null ? void 0 : _b.toLowerCase()) || "main";
  const windowID = rawId.split("?")[0] || "main";
  const transformedRoutes = toLowerCaseKeys(routes);
  const Route = () => transformedRoutes[windowID];
  const router = (0, import_react.useMemo)(() => createHashRouter(createRoutesFromChildren(Route()), {
    basename: `/${windowID}`
  }), [
    windowID
  ]);
  return (0, import_jsx_runtime.jsx)(RouterProvider, {
    ..._providerProps && _providerProps,
    router
  });
}
function createFileRoute(path, id, options) {
  const _options = options || {};
  let url = `/${id}`;
  if (options == null ? void 0 : options.query) {
    const query = new URLSearchParams(options.query).toString();
    url = `${url}?${query}#/${id}`;
  }
  _options.hash = url;
  return [
    path,
    _options
  ];
}
function removeURLExtraDoubleSlashes(url) {
  return url.replace(/([^:]\/)\/+/g, "$1");
}
function createURLRoute(route, id, options) {
  let url = `${route}/#/${id}`;
  if (options == null ? void 0 : options.query) {
    const query = new URLSearchParams(options.query).toString();
    url = `${route}?${query}#/${id}?${query}`;
  }
  return removeURLExtraDoubleSlashes(url);
}
function isDev() {
  const { app } = require_electron();
  return !app.isPackaged;
}
var defaults = {
  port: 3e3,
  windowId: "main"
};
function createElectronRouter({ types, devServerUrl, port = defaults.port }) {
  function registerRoute(props) {
    const serverUrl = props.devServerUrl || devServerUrl || `http://localhost:${props.port ?? port}`;
    const windowId = props.id || defaults.windowId;
    if (isDev()) {
      const URLRoute = createURLRoute(serverUrl, windowId, {
        query: props.query
      });
      props.browserWindow.loadURL(URLRoute);
      return;
    }
    const fileRoute = createFileRoute(props.htmlFile, windowId, {
      query: props.query
    });
    props.browserWindow.loadFile(...fileRoute);
  }
  function Router$1(props) {
    return Router(props);
  }
  const settings = {
    port,
    devServerUrl,
    types: {
      strict: (types == null ? void 0 : types.strict) ?? true,
      ids: (types == null ? void 0 : types.ids) ?? [],
      queryKeys: (types == null ? void 0 : types.queryKeys) ?? []
    }
  };
  return {
    Router: Router$1,
    settings,
    registerRoute
  };
}
export {
  createElectronRouter
};
//# sourceMappingURL=electron-router-dom.js.map
