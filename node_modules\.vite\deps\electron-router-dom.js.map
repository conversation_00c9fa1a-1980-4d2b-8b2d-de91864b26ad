{"version": 3, "sources": ["../../electron-router-dom/src/shared/utils/to-lower-case-keys.ts", "../../electron-router-dom/src/renderer/index.tsx", "../../electron-router-dom/src/main/create-file.ts", "../../electron-router-dom/src/shared/utils/remove-url-extra-double-slashes.ts", "../../electron-router-dom/src/main/create-url.ts", "../../electron-router-dom/src/shared/utils/is-dev.ts", "../../electron-router-dom/src/index.ts"], "sourcesContent": ["export function toLowerCaseKeys<T extends Record<string, unknown>>(\n  target: T\n): T {\n  const transformedObject = Object.keys(target).reduce(\n    (acc, key) => ({\n      ...acc,\n      [key.toLowerCase()]: target[key],\n    }),\n    {}\n  )\n\n  return transformedObject as T\n}\n", "import { useMemo } from 'react'\n\nimport {\n  RouterProvider,\n  createHashRouter,\n  createRoutesFromChildren,\n  type RouterProviderProps,\n} from 'react-router-dom'\n\nimport { toLowerCaseKeys } from 'shared/utils/to-lower-case-keys'\n\nexport type RouteDef = Record<string, JSX.Element>\n\nexport type RouterProps<T extends RouteDef> =\n  | T\n  | {\n      _providerProps: Omit<Partial<RouterProviderProps>, 'router'>\n    }\n\n/**\n * Renders a router component based on the provided routes.\n * @process renderer\n */\nexport function Router<T extends RouteDef>({\n  _providerProps,\n  ...routes\n}: RouterProps<T>) {\n  const selectAllSlashes = /\\//g\n\n  const rawId =\n    location.hash.split(selectAllSlashes)?.[1]?.toLowerCase() || 'main'\n\n  const windowID = rawId.split('?')[0] || 'main'\n  const transformedRoutes: RouteDef = toLowerCaseKeys(routes)\n\n  const Route = () => transformedRoutes[windowID]\n\n  const router = useMemo(\n    () =>\n      createHashRouter(createRoutesFromChildren(Route()), {\n        basename: `/${windowID}`,\n      }),\n    [windowID]\n  )\n\n  return (\n    <RouterProvider {...(_providerProps && _providerProps)} router={router} />\n  )\n}\n", "import type { LoadFileOptions } from 'electron'\n\nexport function createFileRoute(\n  path: string,\n  id: string,\n  options?: Omit<LoadFileOptions, 'hash'>\n): [string, LoadFileOptions] {\n  const _options = (options || {}) as LoadFileOptions\n\n  let url = `/${id}`\n\n  if (options?.query) {\n    const query = new URLSearchParams(options.query).toString()\n\n    url = `${url}?${query}#/${id}`\n  }\n\n  _options.hash = url\n\n  return [path, _options]\n}\n", "export function removeURLExtraDoubleSlashes(url: string) {\n  return url.replace(/([^:]\\/)\\/+/g, '$1')\n}\n", "import type { LoadFileOptions } from 'electron'\n\nimport { removeURLExtraDoubleSlashes } from 'shared/utils/remove-url-extra-double-slashes'\n\nexport function createURLRoute(\n  route: string,\n  id: string,\n  options?: Omit<LoadFileOptions, 'hash'>\n) {\n  let url = `${route}/#/${id}`\n\n  if (options?.query) {\n    const query = new URLSearchParams(options.query).toString()\n\n    url = `${route}?${query}#/${id}?${query}`\n  }\n\n  return removeURLExtraDoubleSlashes(url)\n}\n", "/**\n * @description Check if the app is running in development mode\n * @process main\n * @returns boolean\n */\nexport function isDev() {\n  const { app } = require('electron')\n\n  return !app.isPackaged\n}\n", "import type { <PERSON><PERSON><PERSON><PERSON>indow } from 'electron'\n\nimport type {\n  LiteralUnion,\n  TrueCondition,\n  ElectronRouterOutput,\n} from './shared/types'\n\nimport {\n  type RouteDef,\n  type RouterProps,\n  Router as RendererRouter,\n} from './renderer'\n\nimport { createFileRoute } from './main/create-file'\nimport { createURLRoute } from './main/create-url'\nimport { isDev } from './shared/utils/is-dev'\n\nexport type { Query } from './shared/types'\n\nconst defaults = { port: 3000, windowId: 'main' } as const\n\nexport function createElectronRouter<\n  const T extends {\n    /**\n     * @description The port where the dev server is running.\n     * Only necessary if you are not using the devServerUrl property and you are not using the default port.\n     * @default 3000\n     */\n    port?: number\n\n    /**\n     * @description The URL of the dev server is running.\n     * If not provided, it will use the default URL: `http://localhost:${port}`\n     */\n    devServerUrl?: string\n\n    /**\n     * @description The types definition for the router\n     */\n    types?: {\n      /**\n       * @description Enable or disable strict mode\n       * @default true\n       */\n      strict?: boolean\n\n      /**\n       * @description The IDs of the routes to represent each of the browser windows you will use.\n       * Think of it as a basename of a route.\n       */\n      ids?: string[]\n\n      /**\n       * @description The query keys that will be used in the URLSearchParams\n       */\n      queryKeys?: string[]\n    }\n  },\n>({ types, devServerUrl, port = defaults.port }: ElectronRouterOutput<T>) {\n  type Types = NonNullable<T['types']>\n  type IsStrictMode = Types['strict'] extends boolean ? Types['strict'] : true\n\n  /**\n   * @description Registers a BrowserWindow to a route\n   * @process main\n   */\n  function registerRoute<\n    const S extends {\n      /**\n       * @description The ID of the route to represent the browser window you are using.\n       * Think of it as a basename for this window/route.\n       */\n      id: Types['ids'] extends string[]\n        ? TrueCondition<\n            IsStrictMode,\n            Types['ids'][number],\n            LiteralUnion<Types['ids'][number], string>\n          >\n        : string\n\n      /**\n       * @description The query string in object format to be used in the\n       * **URLSearchParams** / **useSearchParams**\n       */\n      query?: Types['queryKeys'] extends string[]\n        ? TrueCondition<\n            IsStrictMode,\n            Partial<Record<Types['queryKeys'][number], unknown>>,\n            Partial<\n              Record<LiteralUnion<Types['queryKeys'][number], string>, unknown>\n            >\n          >\n        : Record<string, unknown>\n\n      /**\n       * @description The port where the dev server is running.\n       *  If a port is already defined in **createElectronRouter** settings,\n       *  that port will not be used in this **registerRoute** in favor of this new one.\n       */\n      port?: number\n\n      /**\n       * @description The path to dev server URL.\n       * Recommended for HMR (Hot Module Replacement) or cases you need full control over the URL.\n       * If not provided, it will use the default URL: `http://localhost:${port}`\n       * or the one defined in **createElectronRouter** settings.\n       */\n      devServerUrl?: string\n\n      /**\n       * @description The path to the HTML file related to the BrowserWindow\n       */\n      htmlFile: string\n\n      browserWindow: BrowserWindow\n    },\n  >(props: S) {\n    const serverUrl =\n      props.devServerUrl ||\n      devServerUrl ||\n      `http://localhost:${props.port ?? port}`\n\n    const windowId = props.id || defaults.windowId\n\n    if (isDev()) {\n      const URLRoute = createURLRoute(serverUrl, windowId, {\n        query: props.query as Record<string, string>,\n      })\n\n      props.browserWindow.loadURL(URLRoute)\n\n      return\n    }\n\n    const fileRoute = createFileRoute(props.htmlFile, windowId, {\n      query: props.query as Record<string, string>,\n    })\n\n    props.browserWindow.loadFile(...fileRoute)\n  }\n\n  /**\n   * @description The router component\n   * @process renderer\n   */\n  function Router(\n    props: Partial<\n      RouterProps<\n        Types['ids'] extends string[]\n          ? IsStrictMode extends true\n            ? Record<Types['ids'][number], JSX.Element>\n            : Record<LiteralUnion<Types['ids'][number], string>, JSX.Element>\n          : Record<string, JSX.Element>\n      >\n    >\n  ) {\n    return RendererRouter(props as RouteDef)\n  }\n\n  const settings = {\n    port,\n    devServerUrl,\n\n    types: {\n      strict: types?.strict ?? true,\n      ids: types?.ids ?? [],\n      queryKeys: types?.queryKeys ?? [],\n    },\n  } as {\n    port: T['port'] extends number ? T['port'] : typeof defaults.port\n    devServerUrl: T['devServerUrl']\n\n    types: {\n      strict: Types['strict'] extends boolean ? Types['strict'] : true\n      ids: Types['ids'] extends string[] ? Types['ids'] : []\n      queryKeys: Types['queryKeys'] extends string[] ? Types['queryKeys'] : []\n    }\n  }\n\n  return {\n    Router,\n    settings,\n    registerRoute,\n  } as const\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAO,SAASA,gBACdC,QAAS;AAET,QAAMC,oBAAoBC,OAAOC,KAAKH,MAAAA,EAAQI,OAC5C,CAACC,KAAKC,SAAS;IACb,GAAGD;IACH,CAACC,IAAIC,YAAW,CAAA,GAAKP,OAAOM,GAAI;EAClC,IACA,CAAA,CAAC;AAGH,SAAOL;AACT;ACWO,SAASO,OAA2B,EACzCC,gBACA,GAAGC,OACY,GAAA;;AACf,QAAMC,mBAAmB;AAEzB,QAAMC,UACJC,oBAASC,KAAKC,MAAMJ,gBAAmB,MAAvCE,mBAAwC,OAAxCA,mBAA4CN,kBAAiB;AAE/D,QAAMS,WAAWJ,MAAMG,MAAM,GAAI,EAAC,CAAA,KAAM;AACxC,QAAME,oBAA8BlB,gBAAgBW,MAAAA;AAEpD,QAAMQ,QAAQ,MAAMD,kBAAkBD,QAAS;AAE/C,QAAMG,aAASC,sBACb,MACEC,iBAAiBC,yBAAyBJ,MAAU,CAAA,GAAA;IAClDK,UAAU,IAAIP,QAAAA;GAElB,GAAA;IAACA;EAAS,CAAA;AAGZ,aACEQ,wBAACC,gBAAAA;IAAgB,GAAIhB,kBAAkBA;IAAiBU;;AAE5D;AC9CO,SAASO,gBACdC,MACAC,IACAC,SAAuC;AAEvC,QAAMC,WAAYD,WAAW,CAAA;AAE7B,MAAIE,MAAM,IAAIH,EAAAA;AAEd,MAAIC,mCAASG,OAAO;AAClB,UAAMA,QAAQ,IAAIC,gBAAgBJ,QAAQG,KAAK,EAAEE,SAAQ;AAEzDH,UAAM,GAAGA,GAAI,IAAGC,KAAM,KAAIJ,EAAAA;EAC5B;AAEAE,WAAShB,OAAOiB;AAEhB,SAAO;IAACJ;IAAMG;EAAS;AACzB;ACpBO,SAASK,4BAA4BJ,KAAW;AACrD,SAAOA,IAAIK,QAAQ,gBAAgB,IAAA;AACrC;ACEO,SAASC,eACdC,OACAV,IACAC,SAAuC;AAEvC,MAAIE,MAAM,GAAGO,KAAAA,MAAWV,EAAAA;AAExB,MAAIC,mCAASG,OAAO;AAClB,UAAMA,QAAQ,IAAIC,gBAAgBJ,QAAQG,KAAK,EAAEE,SAAQ;AAEzDH,UAAM,GAAGO,KAAM,IAAGN,KAAAA,KAAUJ,EAAG,IAAGI,KAAAA;EACpC;AAEA,SAAOG,4BAA4BJ,GAAAA;AACrC;ACbO,SAASQ,QAAAA;AACd,QAAM,EAAEC,IAAG,IAAKC;AAEhB,SAAO,CAACD,IAAIE;AACd;ACWA,IAAMC,WAAW;EAAEC,MAAM;EAAMC,UAAU;AAAO;AAEzC,SAASC,qBAqCd,EAAEC,OAAOC,cAAcJ,OAAOD,SAASC,KAAI,GAA2B;AAQtE,WAASK,cAkDPC,OAAQ;AACR,UAAMC,YACJD,MAAMF,gBACNA,gBACA,oBAAoBE,MAAMN,QAAQA,IAAAA;AAEpC,UAAMC,WAAWK,MAAMtB,MAAMe,SAASE;AAEtC,QAAIN,MAAS,GAAA;AACX,YAAMa,WAAWf,eAAec,WAAWN,UAAU;QACnDb,OAAOkB,MAAMlB;MACf,CAAA;AAEAkB,YAAMG,cAAcC,QAAQF,QAAAA;AAE5B;IACF;AAEA,UAAMG,YAAY7B,gBAAgBwB,MAAMM,UAAUX,UAAU;MAC1Db,OAAOkB,MAAMlB;IACf,CAAA;AAEAkB,UAAMG,cAAcI,SAAYF,GAAAA,SAAAA;EAClC;AAMA,WAAS/C,SACP0C,OAQC;AAED,WAAOQ,OAAeR,KAAAA;EACxB;AAEA,QAAMS,WAAW;IACff;IACAI;IAEAD,OAAO;MACLa,SAAQb,+BAAOa,WAAU;MACzBC,MAAKd,+BAAOc,QAAO,CAAA;MACnBC,YAAWf,+BAAOe,cAAa,CAAA;IACjC;EACF;AAWA,SAAO;IACLtD,QAAAA;IACAmD;IACAV;EACF;AACF;", "names": ["toLowerCaseKeys", "target", "transformedObject", "Object", "keys", "reduce", "acc", "key", "toLowerCase", "Router", "_providerProps", "routes", "selectAllSlashes", "rawId", "location", "hash", "split", "windowID", "transformedRoutes", "Route", "router", "useMemo", "createHashRouter", "createRoutesFromChildren", "basename", "_jsx", "RouterProvider", "createFileRoute", "path", "id", "options", "_options", "url", "query", "URLSearchParams", "toString", "removeURLExtraDoubleSlashes", "replace", "createURLRoute", "route", "isDev", "app", "require", "isPackaged", "defaults", "port", "windowId", "createElectronRouter", "types", "devServerUrl", "registerRoute", "props", "serverUrl", "URLRoute", "browserWindow", "loadURL", "fileRoute", "htmlFile", "loadFile", "RendererRouter", "settings", "strict", "ids", "query<PERSON>eys"]}